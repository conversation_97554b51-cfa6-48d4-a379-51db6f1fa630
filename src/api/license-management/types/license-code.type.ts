import { UploadFiles } from "element-plus";
/**
 * 授权码分页查询对象
 */
export interface LicenseCodePageQuery extends PageQuery {
  /** 销售订单编号 */
  salesOrderNo: string;
  /** 销售项目名称 */
  salesProjectName: string;
  /** 最终客户名称 */
  finalCustomerName: string;
  /** 项目销售 */
  sellerName: string;
  /** 授权类型 */
  licenseType: string;
  /** 授权产品信息 */
  licenseCodeInfo: string;
}

/** 授权申请表单类型 */
export interface ApplyForm {
  /** 授权类型 */
  licenseType: string;
  /** 销售订单编号 */
  salesOrderNo: string;
  /** 销售项目名称 */
  salesProjectName: string;
  /** 最终客户名称 */
  finalCustomerName: string;
  /** 项目销售 */
  sellerName: string;
  /** 有效期 */
  validType: string;
  /** 普通账号并发登录数量 */
  permitUserCnt: number;
  /** 管理员账号并发登录数量 */
  permitMgrCnt: number;
  /** 证明文件 */
  documentationList: UploadFiles;
}
/** 申请成功信息类型 */
export interface ApplySuccessInfo {
  id: string;
  salesOrderNo?: string;
  salesProjectName?: string;
  finalCustomerName?: string;
  sellerName?: string;
  validType?: string;
  terminalLicenseCount?: string;
  licenseType?: string;
  licenseProductInfo?: string;
  licenseCodeInfo?: string;
  applicant?: string;
  applyDate?: string;
}
/** 授权码回显信息类型 */
export interface LicenseCodeSalesInfo {
  /** 销售订单编号 */
  salesOrderNo: string;
  /** 销售项目名称 */
  salesProjectName: string;
  /** 最终客户名称 */
  finalCustomerName: string;
  /** 项目销售 */
  sellerName: string;
}
