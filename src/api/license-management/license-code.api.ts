import request from "@/utils/request";
import { ApplySuccessInfo, LicenseCodeSalesInfo } from "./types/license-code.type";
const LICENSE_CODE_BASE_URL = "/license-code";

const LicenseCodeAPI = {
  listPageQuery(params: any) {
    return request({
      url: `${LICENSE_CODE_BASE_URL}/listPageQuery`,
      method: "get",
      params,
    });
  },

  apply(data: FormData) {
    return request<any, ApplySuccessInfo>({
      url: `${LICENSE_CODE_BASE_URL}/apply`,
      method: "post",
      data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  // 下载证明文件
  downloadDocumentation(url: string) {
    return request({
      url: `${LICENSE_CODE_BASE_URL}/downloadDocumentation`,
      method: "get",
      params: { url },
      responseType: "blob",
    });
  },

  // 下载授权文件
  downloadLicenseFile(id: string) {
    return request({
      url: `${LICENSE_CODE_BASE_URL}/downloadLicenseFile`,
      method: "get",
      params: { id },
      responseType: "blob",
    });
  },

  // 通过授权码获取授权码销售信息回显
  salesInfoByCode(licenseCode: string) {
    return request<any, LicenseCodeSalesInfo>({
      url: `${LICENSE_CODE_BASE_URL}/salesInfoByCode`,
      method: "get",
      params: { licenseCode },
    });
  },
};

export default LicenseCodeAPI;
