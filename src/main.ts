import { createApp } from "vue";
import App from "./App.vue";
import setupPlugins from "@/plugins";

// 暗黑主题样式
import "element-plus/theme-chalk/dark/css-vars.css";
import "vxe-table/lib/style.css";
// 暗黑模式自定义变量
import "@/styles/dark/css-vars.css";
import "@/styles/index.scss";
import "uno.css";

// 过渡动画
import "animate.css";

// 自动为某些默认事件（如 touchstart、wheel 等）添加 { passive: true },提升滚动性能并消除控制台的非被动事件监听警告
import "default-passive-events";

// VXE TABLE 分页表格组件
import VxePageTable from "@/components/VxePageTable/index.vue";

const app = createApp(App);

// 注册插件
app.use(setupPlugins);
app.mount("#app");

// 注册组件
app.component("VxePageTable", VxePageTable);
