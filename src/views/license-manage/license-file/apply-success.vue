<template>
  <div class="app-container">
    <el-card header="授权绑定申请">
      <el-result icon="success" title="申请成功" subTitle="授权文件信息如下，请下载"></el-result>
      <el-descriptions class="descriptions" :column="1" direction="horizontal">
        <el-descriptions-item label="销售订单编号">{{ data.salesOrderNo }}</el-descriptions-item>
        <el-descriptions-item label="销售项目名称">
          {{ data.salesProjectName }}
        </el-descriptions-item>
        <el-descriptions-item label="最终客户名称">
          {{ data.finalCustomerName }}
        </el-descriptions-item>
        <el-descriptions-item label="项目销售">{{ data.sellerName }}</el-descriptions-item>
        <el-descriptions-item label="有效期">{{ data.validType }}</el-descriptions-item>
        <el-descriptions-item label="账号并发登录数量">
          {{ data.terminalLicenseCount }}
        </el-descriptions-item>
        <el-descriptions-item label="授权类型">{{ data.licenseType }}</el-descriptions-item>
        <el-descriptions-item label="授权产品信息">
          {{ data.licenseProductInfo }}
        </el-descriptions-item>
        <el-descriptions-item label="授权码信息">{{ data.licenseCodeInfo }}</el-descriptions-item>
        <el-descriptions-item label="服务器机器码">{{ data.machineCode }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ data.applicant }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ data.applyDate }}</el-descriptions-item>
      </el-descriptions>
      <div class="footer">
        <el-button type="primary" :loading="downloadLoading" @click="downloadLicenseFile()">
          下载文件
        </el-button>
        <el-button @click="goBack()">返 回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import Base64 from "@/utils/base64";
  import { useRoute, useRouter } from "vue-router";
  import LicenseFileAPI from "@/api/license-manage/license-file.api";
  import { downloadFile } from "@/utils";

  const route = useRoute();
  const router = useRouter();
  const data = Base64.decode(route.query.info);

  // 下载文件
  const downloadLoading = ref(false);
  const downloadLicenseFile = () => {
    downloadLoading.value = true;
    LicenseFileAPI.downloadLicenseFile(data.id).then((response) => {
      downloadFile(response.data, "授权文件.lic");
      downloadLoading.value = false;
    });
  };

  // 返回
  const goBack = () => {
    router.back();
  };
</script>

<style scoped>
  .descriptions {
    display: flex;
    justify-content: center;
  }
  .descriptions >>> .el-descriptions__body .el-descriptions__cell {
    text-align: center;
  }
  .el-result {
    padding: 0;
    padding-bottom: 20px;
  }
  .footer {
    display: flex;
    justify-content: center;
  }
</style>
