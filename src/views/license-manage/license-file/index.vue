<template>
  <div class="app-container">
    <el-container>
      <el-header class="query-container">
        <el-form ref="queryFormRef" :model="queryForm" label-width="100px">
          <el-row type="flex" :gutter="50" justify="center">
            <el-col :span="6">
              <el-form-item label="销售订单编号" prop="salesOrderNo">
                <el-input
                  v-model="queryForm.salesOrderNo"
                  placeholder="请输入销售订单编号"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="销售项目名称" prop="salesProjectName">
                <el-input
                  v-model="queryForm.salesProjectName"
                  placeholder="请输入销售项目名称"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="最终客户名称" prop="finalCustomerName">
                <el-input
                  v-model="queryForm.finalCustomerName"
                  placeholder="请输入最终客户名称"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" :gutter="50" justify="center">
            <el-col :span="6">
              <el-form-item label="服务器机器码" prop="machineCode">
                <el-input
                  v-model="queryForm.machineCode"
                  placeholder="请输入服务器机器码"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权类型" prop="licenseType">
                <el-select v-model="queryForm.licenseType" placeholder="请选择授权类型" clearable>
                  <el-option v-for="it in LICENSE_TYPE_OPTIONS" :label="it" :value="it"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="申请人" prop="applicant">
                <el-input
                  v-model="queryForm.applicant"
                  placeholder="请输入申请人姓名"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="center">
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="pageQuery()">查询</el-button>
                <el-button @click="resetQuery()">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-header>
      <el-main class="table-container">
        <el-button type="primary" class="apply-btn" @click="applyForLicenseBinding()">
          授权绑定申请
        </el-button>
        <VxePageTable ref="tableRef" height="380" :fetch="fetchTableData" :query-param="queryForm">
          <vxe-column type="seq" width="70"></vxe-column>
          <vxe-column field="salesOrderNo" title="销售订单编号" min-width="120"></vxe-column>
          <vxe-column field="salesProjectName" title="销售项目名称" min-width="120"></vxe-column>
          <vxe-column field="finalCustomerName" title="最终客户名称" min-width="120"></vxe-column>
          <vxe-column field="sellerName" title="项目销售" min-width="120"></vxe-column>
          <vxe-column field="licenseProductInfo" title="授权产品信息" min-width="220"></vxe-column>
          <vxe-column field="licenseCodeInfo" title="授权码信息" min-width="320"></vxe-column>
          <vxe-column
            field="terminalLicenseCount"
            title="终端许可数量"
            min-width="220"
          ></vxe-column>
          <vxe-column field="validType" title="有效期" min-width="120"></vxe-column>
          <vxe-column field="licenseType" title="授权类型" min-width="120"></vxe-column>
          <vxe-column field="machineCode" title="服务器机器码" min-width="120"></vxe-column>
          <vxe-column field="machineCancelCode" title="服务器注销码" min-width="120"></vxe-column>
          <vxe-column field="applicant" title="申请人" min-width="120"></vxe-column>
          <vxe-column field="applyDate" title="申请时间" min-width="120"></vxe-column>
          <vxe-column field="documentation" title="操作" fixed="right" min-width="120">
            <template #default="{ row }">
              <el-button
                type="text"
                size="small"
                plain
                :loading="row.downloadLoading"
                @click="downloadLicenseFile(row)"
              >
                下载授权文件
              </el-button>
            </template>
          </vxe-column>
        </VxePageTable>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from "vue";
  import { LicenseFilePageQuery } from "@/api/license-manage/types/license-file.type";
  import LicenseFileAPI from "@/api/license-manage/license-file.api";
  import { downloadFile } from "@/utils";
  import { useRouter } from "vue-router";
  import { LICENSE_TYPE_OPTIONS } from "@/constants";

  const router = useRouter();

  const queryFormRef = ref();

  const queryForm = ref({
    salesOrderNo: "",
    salesProjectName: "",
    finalCustomerName: "",
    machineCode: "",
    licenseType: "",
    applicant: "",
  });

  const tableRef = ref();

  // 拉取数据
  function fetchTableData(params: LicenseFilePageQuery) {
    return LicenseFileAPI.listPageQuery(params);
  }

  // 查询
  function pageQuery() {
    tableRef.value.refresh();
  }

  // 重置查询
  function resetQuery() {
    queryFormRef.value.resetFields();
    pageQuery();
  }
  function downloadLicenseFile(row: any) {
    row.downloadLoading = true;
    LicenseFileAPI.downloadLicenseFile(row.id).then((response) => {
      downloadFile(response.data, "授权文件.lic");
      row.downloadLoading = false;
    });
  }

  // 授权绑定申请
  function applyForLicenseBinding() {
    router.push("/license-file/apply");
  }
</script>

<style scoped>
  .query-container {
    height: 200px;
    padding: 50px;
    margin-bottom: 20px;
    background-color: #fff;
  }
  .table-container {
    background-color: #fff;
  }
  .table-container > .apply-btn {
    margin-bottom: 10px;
  }
</style>
