<template>
  <div>
    <vxe-table
      class="vxe-page-table"
      ref="xTable"
      border
      resizable
      show-header-overflow
      show-overflow
      highlight-hover-row
      :loading="tableLoading"
      :data="tableData.rows"
      :height="height"
    >
      <slot></slot>
    </vxe-table>
    <footer>
      <vxe-pager
        :current-page="tableData.pageNumber"
        :page-size="tableData.pageSize"
        :total="tableData.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange"
      />
    </footer>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, toRaw } from "vue";
  import type { VxePagerDefines } from "vxe-table";
  defineOptions({
    name: "VxePageTable",
  });

  let props = defineProps({
    // 组件初始化时是否自动查询数据
    initQuery: {
      type: Boolean,
      default: true,
    },
    // 查询条件参数
    queryParam: {
      type: Object,
      default: null,
    },
    // 拉取数据
    fetch: {
      type: Function,
      default: () => {
        return Promise.resolve({
          rows: [],
          pageNumber: 1,
          pageSize: 20,
          total: 0,
        });
      },
    },
    // 表格高度
    height: {
      type: String,
      default: null,
    },
  });

  let tableLoading = ref(false);
  let tableData = reactive({
    pageNumber: 1,
    pageSize: 20,
    total: 0,
    rows: [],
  });
  onMounted(() => {
    if (props.initQuery) {
      fetchData();
    }
  });
  async function fetchData() {
    tableLoading.value = true;
    const query = Object.assign({}, props.queryParam);
    query.pageNumber = tableData.pageNumber;
    query.pageSize = tableData.pageSize;
    const res = await props.fetch(toRaw(query));
    tableLoading.value = false;
    tableData.total = res.total;
    tableData.rows = res.rows;
  }

  function refresh(reset = false) {
    if (reset === true) {
      tableData.pageNumber = 1;
    }
    return fetchData();
  }
  function handlePageChange({ currentPage, pageSize }: VxePagerDefines.PageChangeEventParams) {
    tableData.pageNumber = currentPage;
    tableData.pageSize = pageSize;
    fetchData();
  }
  defineExpose({ refresh });
</script>

<style scoped></style>
